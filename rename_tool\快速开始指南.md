# WFS文件名批量更名工具 - 快速开始指南

## 🚀 快速开始

### 1. 最简单的使用方式

```bash
# 使用默认配置处理默认数据目录
wfs_rename_tool.exe

# 指定数据目录
wfs_rename_tool.exe C:\your\wfsdata
```

### 2. 使用配置文件

```bash
# 使用主配置文件
wfs_rename_tool.exe -config wfs_rename_tool_config.json

# 使用预设配置
wfs_rename_tool.exe -config config_large.json    # 大数据量
wfs_rename_tool.exe -config config_small.json    # 小数据量
wfs_rename_tool.exe -config config_ultra.json    # 超大数据量
```

## 📋 配置文件说明

### 主配置文件: `wfs_rename_tool_config.json`

这是最完整的配置文件，包含所有参数和说明：

```json
{
  "dataDir": "../wfsdata",
  "workerCount": 16,
  "batchSize": 500,
  "logLevel": "INFO",
  "channelBuffer": 32,
  "progressStep": 10000
}
```

### 预设配置文件

| 配置文件 | 适用场景 | 记录数量 | 特点 |
|----------|----------|----------|------|
| `config_small.json` | 小数据量 | < 100万 | 低资源消耗 |
| `config_large.json` | 大数据量 | > 1000万 | 高性能处理 |
| `config_ultra.json` | 超大数据量 | > 1亿 | 最大并发度 |

## 🎯 根据数据量选择配置

### 如何估算数据量？

```bash
# 运行程序查看总记录数（会在开始时显示）
wfs_rename_tool.exe -config config_small.json
```

输出示例：
```
准备处理 50000000 条记录...  # 这里显示总记录数
```

### 配置选择建议

```bash
# < 100万条记录
wfs_rename_tool.exe -config config_small.json

# 100万 - 1000万条记录  
wfs_rename_tool.exe -config wfs_rename_tool_config.json

# > 1000万条记录
wfs_rename_tool.exe -config config_large.json

# > 1亿条记录
wfs_rename_tool.exe -config config_ultra.json
```

## 📊 命令行参数完整列表

### 基本语法

```bash
wfs_rename_tool.exe [选项] [数据目录]
```

### 所有选项

| 选项 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `-config` | `-c` | 配置文件路径 | `-config config.json` |
| `-data` | `-d` | 数据目录路径 | `-data C:\wfsdata` |
| `-help` | `-h` | 显示帮助 | `-h` |

### 使用示例

```bash
# 1. 基本使用
wfs_rename_tool.exe ../wfsdata

# 2. 使用配置文件
wfs_rename_tool.exe -config wfs_rename_tool_config.json

# 3. 配置文件 + 数据目录（数据目录优先级更高）
wfs_rename_tool.exe -config config_large.json -data C:\wfsdata

# 4. 简写形式
wfs_rename_tool.exe -c config.json -d C:\wfsdata

# 5. 查看帮助
wfs_rename_tool.exe -h
```

## ⚙️ 自定义配置

### 创建自定义配置文件

```bash
# 复制主配置文件
copy wfs_rename_tool_config.json my_config.json

# 编辑配置文件
notepad my_config.json
```

### 配置参数说明

```json
{
  "dataDir": "../wfsdata",        // 数据目录路径
  "workerCount": 16,              // 并发线程数 (建议: CPU核心数 × 1-4)
  "batchSize": 500,               // 批处理大小 (建议: 100-2000)
  "logLevel": "INFO",             // 日志级别: DEBUG, INFO, WARN, ERROR
  "channelBuffer": 32,            // 通道缓冲区 (建议: workerCount × 2)
  "progressStep": 10000           // 进度报告频率
}
```

### 性能调优建议

#### CPU性能优先
```json
{
  "workerCount": 32,    // 高并发
  "batchSize": 1000,    // 大批处理
  "channelBuffer": 64   // 大缓冲区
}
```

#### 内存限制环境
```json
{
  "workerCount": 4,     // 低并发
  "batchSize": 50,      // 小批处理
  "channelBuffer": 8    // 小缓冲区
}
```

## 🔍 监控和调试

### 启用详细日志

```json
{
  "logLevel": "DEBUG",
  "progressStep": 100
}
```

### 查看处理进度

程序运行时会显示实时进度：

```
进度: 45.67% (4567/10000) | 成功: 3456 | 删除: 1111 | 错误: 0 | 用时: 2m30s | 预计剩余: 3m15s
```

### 日志文件位置

- 主日志: `数据目录/logs/rename_tool.log`
- 系统日志: `数据目录/logs/wfs.log`

## ⚠️ 注意事项

### 运行前检查

1. **备份数据**: 建议运行前备份WFS数据库
2. **停止服务**: 确保没有其他程序访问数据库
3. **检查权限**: 确保对数据目录有读写权限
4. **磁盘空间**: 确保有足够空间用于日志文件

### 安全建议

```bash
# 1. 先在测试环境验证
wfs_rename_tool.exe -config config_small.json -data test_wfsdata

# 2. 使用小批量测试
# 修改配置文件: "batchSize": 10, "progressStep": 1

# 3. 检查日志文件
type "数据目录\logs\rename_tool.log"
```

## 🆘 故障排除

### 常见错误及解决方案

#### 1. 配置文件错误
```
加载配置文件失败: 解析配置文件失败
```
**解决**: 检查JSON格式是否正确

#### 2. 数据目录错误
```
初始化失败: 创建日志目录失败
```
**解决**: 检查数据目录路径和权限

#### 3. 内存不足
```
程序运行缓慢或崩溃
```
**解决**: 使用 `config_small.json` 或减少并发参数

### 获取帮助

```bash
# 查看帮助信息
wfs_rename_tool.exe -h

# 查看版本信息（在帮助中显示）
wfs_rename_tool.exe -h | findstr "版本"
```

## 📈 性能参考

### 处理速度参考

| 数据量 | 配置 | 预计时间 | 内存使用 |
|--------|------|----------|----------|
| 10万条 | small | 1-5分钟 | < 50MB |
| 100万条 | default | 10-30分钟 | < 100MB |
| 1000万条 | large | 2-6小时 | < 200MB |
| 1亿条 | ultra | 10-50小时 | < 500MB |

### 系统要求

- **最低**: 4核CPU, 4GB内存, 100MB可用空间
- **推荐**: 8核CPU, 8GB内存, 1GB可用空间
- **高性能**: 16核CPU, 16GB内存, 10GB可用空间

---

**提示**: 首次使用建议从小数据量开始测试，熟悉工具后再处理大规模数据。
