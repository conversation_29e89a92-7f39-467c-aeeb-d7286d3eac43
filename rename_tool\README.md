# WFS文件名批量更名工具

## 项目概述

本工具基于原WFS文件存储系统改造，去除了web服务和thrift协议服务，专门用于批量处理数据库中的文件名更名操作。

## 主要功能

1. **文件名标准化**: 将数据库中带有路径的文件名修改为filestem（去除路径和扩展名）
2. **重复检测**: 检查目标文件名是否已存在
3. **重复处理**: 如果目标文件名已存在，则删除当前记录
4. **并发处理**: 支持多线程并发处理，提高处理效率
5. **进度显示**: 实时显示处理进度和统计信息

## 技术特点

- **高性能**: 基于原WFS系统的高性能存储引擎
- **并发安全**: 使用worker pool模式，支持并发处理
- **实时监控**: 提供详细的进度显示和统计信息
- **错误处理**: 完善的错误处理和日志记录机制

## 编译方法

### 前置条件

1. Go 1.22或更高版本
2. CGO支持（需要C编译器）
3. MinGW-w64开发工具包（Windows环境）

### 编译步骤

1. 进入工具目录：
```bash
cd rename_tool
```

2. 运行编译脚本：
```bash
build.bat
```

或者手动编译：
```bash
go build -O -trimpath -ldflags="-s -w -linkmode=external" -tags=release -o wfs_rename_tool.exe .
```

## 使用方法

### 基本用法

```bash
# 使用默认数据目录（../wfsdata）
wfs_rename_tool.exe

# 指定数据目录
wfs_rename_tool.exe C:\path\to\wfsdata
```

### 参数说明

- `数据目录`: WFS数据库所在目录，默认为 `../wfsdata`

### 配置参数

工具内置以下配置参数（可在代码中修改）：

- **并发线程数**: 默认为CPU核心数
- **批处理大小**: 默认为100条记录
- **日志级别**: 默认为INFO

## 处理流程

1. **初始化**: 加载WFS数据库和配置
2. **扫描**: 分批扫描数据库中的所有文件记录
3. **处理**: 对每条记录执行以下操作：
   - 提取文件stem（去除路径和扩展名）
   - 检查目标stem是否已存在
   - 如果存在则删除当前记录，否则执行更名
4. **统计**: 显示处理结果统计

## 输出示例

```
WFS文件名批量更名工具
========================
数据目录: ../wfsdata
并发线程数: 8
批处理大小: 100

进度: 45.67% (4567/10000) | 成功: 3456 | 删除: 1111 | 错误: 0 | 用时: 2m30s | 预计剩余: 3m15s

处理完成！
总记录数: 10000
已处理数: 10000
成功更名: 8500
删除重复: 1500
错误数量: 0
总用时: 5m45s
```

## 日志文件

工具会在数据目录的logs子目录下生成日志文件：
- `rename_tool.log`: 详细的操作日志

## 注意事项

1. **数据备份**: 运行工具前请备份WFS数据库
2. **独占访问**: 运行期间请确保没有其他程序访问WFS数据库
3. **磁盘空间**: 确保有足够的磁盘空间用于日志文件
4. **权限**: 确保对数据目录有读写权限

## 错误处理

工具具有完善的错误处理机制：
- 数据库连接失败会立即退出
- 单个记录处理失败不会影响其他记录
- 所有错误都会记录到日志文件中

## 性能优化

- 使用批量读取减少数据库访问次数
- 采用worker pool模式实现并发处理
- 内存缓存机制减少重复查询
- 优化的进度显示避免频繁输出

## 技术架构

基于原WFS系统的核心组件：
- **存储引擎**: LevelDB数据库操作
- **并发控制**: Go协程和通道机制
- **日志系统**: 结构化日志记录
- **进度监控**: 原子操作保证线程安全

## 故障排除

### 常见问题

1. **编译失败**: 检查Go版本和CGO环境配置
2. **数据库打开失败**: 检查数据目录路径和权限
3. **内存不足**: 减少并发线程数或批处理大小
4. **处理缓慢**: 增加并发线程数或检查磁盘性能

### 调试模式

修改代码中的日志级别为DEBUG可获得更详细的调试信息：
```go
config.LogLevel = "DEBUG"
```

## 版本信息

- 版本: 1.0.0
- 基于: WFS 1.0.7
- 编译日期: 2025-07-28
