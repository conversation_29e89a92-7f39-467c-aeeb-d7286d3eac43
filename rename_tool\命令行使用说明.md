# WFS文件名批量更名工具 - 命令行使用说明

## 基本语法

```bash
wfs_rename_tool.exe [选项] [数据目录]
```

## 命令行选项

| 选项 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `-config` | `-c` | 指定JSON配置文件路径 | `-config config.json` |
| `-data` | `-d` | 指定数据目录路径 | `-data C:\wfsdata` |
| `-help` | `-h` | 显示帮助信息 | `-h` |

## 使用方式

### 1. 使用默认配置

```bash
# 使用默认配置和默认数据目录 (../wfsdata)
wfs_rename_tool.exe

# 指定数据目录
wfs_rename_tool.exe C:\path\to\wfsdata
wfs_rename_tool.exe ../wfsdata
```

### 2. 使用配置文件

```bash
# 使用配置文件（数据目录从配置文件读取）
wfs_rename_tool.exe -config wfs_rename_tool_config.json

# 使用配置文件的简写形式
wfs_rename_tool.exe -c wfs_rename_tool_config.json
```

### 3. 配置文件 + 数据目录

```bash
# 配置文件 + 命令行指定数据目录（命令行优先级更高）
wfs_rename_tool.exe -config wfs_rename_tool_config.json -data C:\wfsdata
wfs_rename_tool.exe -c config.json -d C:\wfsdata

# 位置参数形式
wfs_rename_tool.exe -config config.json C:\wfsdata
```

## 配置文件格式

### 基本配置文件 (wfs_rename_tool_config.json)

```json
{
  "dataDir": "../wfsdata",
  "workerCount": 16,
  "batchSize": 500,
  "logLevel": "INFO",
  "channelBuffer": 32,
  "progressStep": 10000
}
```

### 完整配置文件示例

```json
{
  "dataDir": "../wfsdata",
  "workerCount": 16,
  "batchSize": 500,
  "logLevel": "INFO",
  "channelBuffer": 32,
  "progressStep": 10000,
  "description": {
    "dataDir": "WFS数据库目录路径",
    "workerCount": "并发工作线程数",
    "batchSize": "批处理大小",
    "logLevel": "日志级别：DEBUG, INFO, WARN, ERROR",
    "channelBuffer": "任务通道缓冲区大小",
    "progressStep": "进度报告步长"
  }
}
```

## 配置参数说明

### 核心参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `dataDir` | string | `"../wfsdata"` | WFS数据库目录路径 |
| `workerCount` | int | CPU核心数 | 并发工作线程数 |
| `batchSize` | int | 100 | 批处理大小 |
| `logLevel` | string | `"INFO"` | 日志级别 |
| `channelBuffer` | int | CPU核心数×2 | 任务通道缓冲区大小 |
| `progressStep` | int | 1000 | 进度报告步长 |

### 数据量配置建议

#### 小数据量 (< 100万条记录)
```json
{
  "workerCount": 8,
  "batchSize": 100,
  "channelBuffer": 16,
  "progressStep": 1000
}
```

#### 中等数据量 (100万-1000万条记录)
```json
{
  "workerCount": 12,
  "batchSize": 300,
  "channelBuffer": 24,
  "progressStep": 5000
}
```

#### 大数据量 (> 1000万条记录)
```json
{
  "workerCount": 16,
  "batchSize": 500,
  "channelBuffer": 32,
  "progressStep": 10000
}
```

#### 超大数据量 (> 1亿条记录)
```json
{
  "workerCount": 24,
  "batchSize": 1000,
  "channelBuffer": 48,
  "progressStep": 50000
}
```

## 使用示例

### 示例1: 基本使用

```bash
# 最简单的使用方式
wfs_rename_tool.exe ../wfsdata
```

**输出**:
```
WFS文件名批量更名工具
========================
数据目录: ../wfsdata
并发线程数: 8
批处理大小: 100
通道缓冲区: 16
进度报告步长: 1000
日志级别: INFO

准备处理 5 条记录...
处理完成！
总记录数: 5
已处理数: 5
成功更名: 5
删除重复: 0
错误数量: 0
```

### 示例2: 使用配置文件

```bash
# 创建配置文件
echo '{
  "dataDir": "C:\\MyWfsData",
  "workerCount": 12,
  "batchSize": 200,
  "logLevel": "DEBUG",
  "channelBuffer": 24,
  "progressStep": 5000
}' > my_config.json

# 使用配置文件
wfs_rename_tool.exe -config my_config.json
```

### 示例3: 大数据量处理

```bash
# 使用预设的大数据量配置
wfs_rename_tool.exe -config wfs_rename_tool_config.json -data /large/wfsdata
```

**配置文件内容**:
```json
{
  "dataDir": "/large/wfsdata",
  "workerCount": 16,
  "batchSize": 500,
  "logLevel": "INFO",
  "channelBuffer": 32,
  "progressStep": 10000
}
```

## 优先级规则

配置参数的优先级从高到低：

1. **命令行参数** (`-data`)
2. **配置文件参数** (`-config`)
3. **位置参数** (第一个非选项参数)
4. **默认配置**

### 示例

```bash
# 配置文件中 dataDir = "/config/path"
# 命令行指定 -data "/cmd/path"
# 位置参数 "/pos/path"
wfs_rename_tool.exe -config config.json -data /cmd/path /pos/path

# 最终使用: /cmd/path (命令行优先级最高)
```

## 错误处理

### 配置文件错误

```bash
wfs_rename_tool.exe -config invalid.json
```

**输出**:
```
加载配置文件失败: 读取配置文件失败: open invalid.json: no such file or directory
使用默认配置继续运行...
```

### 数据目录错误

```bash
wfs_rename_tool.exe /invalid/path
```

**输出**:
```
初始化失败: 创建日志目录失败: mkdir /invalid/path/logs: permission denied
```

## 调试和监控

### 启用调试模式

```json
{
  "logLevel": "DEBUG",
  "progressStep": 100
}
```

### 监控系统资源

```bash
# Windows
tasklist /fi "imagename eq wfs_rename_tool.exe"

# 监控内存使用
wmic process where name="wfs_rename_tool.exe" get PageFileUsage,WorkingSetSize
```

## 性能调优

### CPU密集型优化

```json
{
  "workerCount": 32,
  "batchSize": 1000,
  "channelBuffer": 64
}
```

### 内存限制优化

```json
{
  "workerCount": 4,
  "batchSize": 50,
  "channelBuffer": 8
}
```

### I/O密集型优化

```json
{
  "workerCount": 8,
  "batchSize": 200,
  "channelBuffer": 16
}
```

## 常见问题

### Q: 如何查看当前使用的配置？

A: 程序启动时会显示所有配置参数：

```
数据目录: ../wfsdata
并发线程数: 16
批处理大小: 500
通道缓冲区: 32
进度报告步长: 10000
日志级别: INFO
```

### Q: 配置文件路径支持相对路径吗？

A: 是的，支持相对路径和绝对路径：

```bash
wfs_rename_tool.exe -config ./config.json        # 相对路径
wfs_rename_tool.exe -config C:\config\app.json   # 绝对路径
```

### Q: 如何创建适合我的数据量的配置？

A: 参考配置文件中的预设配置，或根据以下公式：

- `workerCount` = CPU核心数 × (1-4)
- `batchSize` = 记录数 / 1000 (最小100，最大2000)
- `channelBuffer` = workerCount × 2
- `progressStep` = 记录数 / 100

---

**提示**: 建议先在测试环境使用小批量数据验证配置效果，然后再应用到生产环境。
