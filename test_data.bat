@echo off
echo 检查数据文件状态...
echo.

echo === F盘数据目录 ===
dir F:\wfsdata
echo.

echo === 数据库文件 ===
dir F:\wfsdata\wfsdb
echo.

echo === 数据文件 ===
dir F:\wfsdata\wfsfile
echo.

echo === WFS配置文件 ===
type F:\wfs.json
echo.

echo === 启动WFS服务测试 ===
echo 正在启动WFS服务...
start /B wfs.exe -c F:\wfs.json

echo 等待5秒...
timeout /t 5 /nobreak > nul

echo 检查端口状态...
netstat -an | findstr :8080

echo.
echo 如果看到8080端口在LISTENING状态，说明服务启动成功
echo 然后可以访问 http://localhost:8080 查看web界面
pause
