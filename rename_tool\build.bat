@echo off
echo 编译WFS文件名批量更名工具...

REM 清理缓存
go clean -cache
go clean -testcache

REM 设置编译参数
set GOARCH=amd64
set CGO_ENABLED=1
set CC=C:\dev\mingw_devkit-x642.0.0\w64devkit\bin\gcc.exe
set CXX=C:\dev\mingw_devkit-x642.0.0\w64devkit\bin\g++.exe
set PATH=C:\dev\mingw_devkit-x642.0.0\w64devkit\bin;%PATH%

REM 编译
echo 开始编译...
go build -O -trimpath -ldflags="-s -w -linkmode=external" -tags=release -o wfs_rename_tool.exe .

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！生成文件: wfs_rename_tool.exe
    echo.
    echo 使用方法:
    echo   wfs_rename_tool.exe [数据目录]
    echo.
    echo 示例:
    echo   wfs_rename_tool.exe ../wfsdata
) else (
    echo 编译失败！
    exit /b 1
)

pause
