{"sourceDir": "./source", "dataDir": "../wfsdata", "workerCount": 16, "channelBuffer": 32, "logLevel": "INFO", "progressStep": 100, "maxFileSize": 104857600, "skipExtensions": [".tmp", ".log", ".lock", ".bak", ".swp"], "compressType": 0, "description": {"sourceDir": "源目录路径，将遍历此目录下的所有子目录和文件", "dataDir": "WFS数据库目录路径", "workerCount": "并发工作线程数，建议设置为CPU核心数的1-4倍", "channelBuffer": "任务通道缓冲区大小，控制内存使用", "logLevel": "日志级别：DEBUG, INFO, WARN, ERROR", "progressStep": "进度报告步长，每处理多少个文件报告一次进度", "maxFileSize": "最大文件大小限制（字节），默认100MB", "skipExtensions": "跳过的文件扩展名列表", "compressType": "压缩类型：0=默认压缩, 1=无压缩, 2=gzip压缩"}, "presets": {"small": {"description": "小文件量配置（< 1万个文件）", "workerCount": 8, "channelBuffer": 16, "progressStep": 50, "maxFileSize": 52428800}, "medium": {"description": "中等文件量配置（1万-10万个文件）", "workerCount": 12, "channelBuffer": 24, "progressStep": 100, "maxFileSize": 104857600}, "large": {"description": "大文件量配置（> 10万个文件）", "workerCount": 16, "channelBuffer": 32, "progressStep": 500, "maxFileSize": 104857600}, "ultra": {"description": "超大文件量配置（> 100万个文件）", "workerCount": 24, "channelBuffer": 48, "progressStep": 1000, "maxFileSize": 52428800}}, "notes": ["配置文件采用JSON格式", "所有路径支持相对路径和绝对路径", "建议根据文件数量选择合适的预设配置", "可以通过命令行参数覆盖配置文件中的设置", "修改配置后无需重新编译程序"]}