# 大数据量处理配置说明

## 针对超大数据量（如2亿条记录）的优化配置

### 1. 内存优化策略

#### 流式处理架构
- ✅ **已实现**: 采用流式处理，不一次性加载所有记录到内存
- ✅ **已实现**: 分批从数据库读取记录，立即发送到处理队列
- ✅ **已实现**: 使用有限大小的通道缓冲区控制内存使用

#### 配置参数优化
```go
// 针对大数据量的推荐配置
config = &RenameToolConfig{
    DataDir:       "../wfsdata",
    WorkerCount:   16,              // 增加并发线程数
    BatchSize:     500,             // 增加批处理大小
    LogLevel:      "INFO",          // 减少日志输出
    ChannelBuffer: 32,              // 适中的缓冲区大小
    ProgressStep:  10000,           // 每1万条记录报告进度
}
```

### 2. 性能调优建议

#### 并发线程数
- **小数据量** (< 100万): CPU核心数
- **中等数据量** (100万-1000万): CPU核心数 × 2
- **大数据量** (> 1000万): CPU核心数 × 2-4

#### 批处理大小
- **小数据量**: 100条/批
- **中等数据量**: 500条/批  
- **大数据量**: 1000-2000条/批

#### 通道缓冲区
- **原则**: 保持适中大小，避免内存过度使用
- **推荐**: 并发线程数 × 2-4

### 3. 内存使用估算

#### 单条记录内存占用
```
RenameTask结构体: ~100字节
- ID: 8字节
- OldPath: ~50字节 (平均)
- NewPath: ~20字节 (平均)  
- FileStem: ~20字节 (平均)
```

#### 总内存使用
```
内存使用 = 通道缓冲区大小 × 单条记录大小
例如: 32 × 100字节 = 3.2KB (通道缓冲区)
加上工作线程内存: 16 × 1KB = 16KB
总计: < 100KB (极低内存占用)
```

### 4. 处理时间估算

#### 性能基准
- **处理速度**: 约1000-5000条/秒 (取决于硬件)
- **2亿条记录**: 预计11-55小时

#### 影响因素
1. **磁盘I/O**: LevelDB读写性能
2. **CPU性能**: 并发处理能力
3. **内存**: 缓存命中率
4. **网络**: 如果数据库在网络存储上

### 5. 监控和日志

#### 进度监控
```
[INFO] 开始流式处理数据库，总记录数: 200000000, 最大序列号: 200000000
[INFO] 已发送 10000 条记录到处理队列
[INFO] 已发送 20000 条记录到处理队列
...
进度: 5.00% (10000000/200000000) | 成功: 9500000 | 删除: 500000 | 错误: 0
```

#### 关键指标
- 处理速度 (条/秒)
- 内存使用率
- 错误率
- 预计剩余时间

### 6. 故障恢复

#### 断点续传 (未来版本)
```go
// 保存处理进度
type ProcessState struct {
    LastProcessedSeq int64
    ProcessedCount   int64
    SuccessCount     int64
    DeletedCount     int64
}
```

#### 数据一致性
- 每批处理后自动提交事务
- 失败记录单独记录到错误日志
- 支持重新处理失败记录

### 7. 系统资源要求

#### 最低配置
- **CPU**: 4核心
- **内存**: 4GB
- **磁盘**: SSD推荐
- **可用空间**: 数据库大小的20%

#### 推荐配置
- **CPU**: 8-16核心
- **内存**: 8-16GB
- **磁盘**: NVMe SSD
- **可用空间**: 数据库大小的50%

### 8. 使用示例

#### 启动命令
```bash
# 使用默认配置
wfs_rename_tool.exe C:\large_wfsdata

# 监控系统资源
# 在另一个终端运行
tasklist /fi "imagename eq wfs_rename_tool.exe"
```

#### 预期输出
```
WFS文件名批量更名工具
========================
数据目录: C:\large_wfsdata
并发线程数: 16
批处理大小: 500
通道缓冲区: 32

准备处理 200000000 条记录...
开始流式处理数据库，总记录数: 200000000, 最大序列号: 200000000

进度: 0.01% (10000/200000000) | 成功: 9500 | 删除: 500 | 错误: 0 | 用时: 10s | 预计剩余: 55h
进度: 0.02% (20000/200000000) | 成功: 19000 | 删除: 1000 | 错误: 0 | 用时: 20s | 预计剩余: 54h
...
```

### 9. 性能优化技巧

#### 数据库优化
- 确保LevelDB有足够的缓存
- 使用SSD存储提高I/O性能
- 定期进行数据库压缩

#### 系统优化
- 关闭不必要的后台程序
- 设置进程优先级为高
- 监控磁盘空间使用

#### 网络优化
- 如果数据库在网络存储上，确保网络稳定
- 考虑本地缓存策略

### 10. 故障排除

#### 常见问题
1. **内存不足**: 减少并发线程数和缓冲区大小
2. **处理缓慢**: 增加批处理大小，检查磁盘性能
3. **频繁错误**: 检查数据库完整性，降低并发度

#### 调试模式
```go
// 修改配置启用详细日志
config.LogLevel = "DEBUG"
config.ProgressStep = 100  // 更频繁的进度报告
```

---

**注意**: 对于超大数据量处理，建议先在测试环境验证配置，然后再在生产环境运行。
