# WFS高性能文件入库工具

## 项目概述

WFS高性能文件入库工具是一个专门用于将本地文件批量导入到WFS数据库的高性能工具。它能够遍历指定目录的所有子目录和文件，提取文件的stem（去除路径和扩展名）作为键名，将文件内容作为值插入到WFS数据库中。

## 主要功能

1. **递归目录遍历**: 自动遍历指定目录下的所有子目录和文件
2. **高性能并发处理**: 支持多线程并发插入，充分利用系统资源
3. **流式处理**: 采用流式处理架构，支持大量文件而不占用过多内存
4. **实时进度显示**: 显示处理进度、速度和预估剩余时间
5. **智能文件过滤**: 支持按文件大小和扩展名过滤文件
6. **灵活配置**: 支持JSON配置文件和命令行参数

## 技术特点

- **高性能**: 基于WFS系统的高性能存储引擎
- **并发安全**: 使用worker pool模式，支持并发处理
- **内存优化**: 流式处理，内存使用量极低
- **错误处理**: 完善的错误处理和日志记录机制
- **可配置**: 灵活的配置选项适应不同场景

## 编译方法

### 前置条件

1. Go 1.22或更高版本
2. CGO支持（需要C编译器）
3. MinGW-w64开发工具包（Windows环境）

### 编译步骤

1. 进入工具目录：
```bash
cd insert_tool
```

2. 运行编译脚本：
```bash
build.bat
```

或者手动编译：
```bash
go build -o wfs_insert_tool.exe .
```

## 使用方法

### 基本用法

```bash
# 使用默认配置处理默认源目录（./source）
wfs_insert_tool.exe

# 指定源目录
wfs_insert_tool.exe C:\path\to\files

# 使用配置文件
wfs_insert_tool.exe -config wfs_insert_tool_config.json

# 配置文件 + 源目录
wfs_insert_tool.exe -config config_large.json -source C:\files
```

### 命令行参数

- `-config`, `-c` : 指定JSON配置文件路径
- `-source`, `-s` : 指定源目录路径
- `-help`, `-h` : 显示帮助信息

### 配置参数

工具内置以下配置参数：

- **源目录**: 要处理的文件目录
- **并发线程数**: 默认为CPU核心数
- **通道缓冲区**: 控制内存使用
- **最大文件大小**: 默认100MB
- **跳过扩展名**: 默认跳过.tmp, .log, .lock等
- **压缩类型**: 数据压缩方式

## 处理流程

1. **初始化**: 加载WFS数据库和配置
2. **扫描**: 递归遍历源目录的所有文件
3. **过滤**: 根据文件大小和扩展名过滤文件
4. **处理**: 对每个文件执行以下操作：
   - 提取文件stem（去除路径和扩展名）
   - 读取文件内容
   - 插入到WFS数据库
5. **统计**: 显示处理结果统计

## 输出示例

```
WFS高性能文件入库工具
========================
已加载配置文件: config_small.json
源目录: ./source
数据目录: ../wfsdata
并发线程数: 8
通道缓冲区: 16
进度报告步长: 50
最大文件大小: 50.0 MB
跳过扩展名: [.tmp .log .lock .bak]
压缩类型: 0
日志级别: INFO

准备处理目录: ./source

进度: 75.00% (3/4) | 大小: 72.09% (62 B/86 B) | 成功: 3 | 错误: 0 | 用时: 0s | 预计剩余: 0s

处理完成！
总文件数: 4
已处理数: 4
成功插入: 4
错误数量: 0
总文件大小: 86 B
已处理大小: 86 B
总用时: 0s
处理速度: 104.64 文件/秒, 2.2 KB/秒
```

## 配置文件

### 主配置文件 (wfs_insert_tool_config.json)

```json
{
  "sourceDir": "./source",
  "dataDir": "../wfsdata",
  "workerCount": 16,
  "channelBuffer": 32,
  "logLevel": "INFO",
  "progressStep": 100,
  "maxFileSize": 104857600,
  "skipExtensions": [".tmp", ".log", ".lock", ".bak", ".swp"],
  "compressType": 0
}
```

### 预设配置文件

| 配置文件 | 适用场景 | 文件数量 | 特点 |
|----------|----------|----------|------|
| `config_small.json` | 小文件量 | < 1万个 | 低资源消耗 |
| `config_large.json` | 大文件量 | > 10万个 | 高性能处理 |

## 性能优化

### 配置建议

- **并发线程数**: 建议设置为CPU核心数的1-4倍
- **通道缓冲区**: 建议设置为并发线程数的2倍
- **批处理大小**: 根据文件数量调整进度报告频率

### 性能基准

- **处理速度**: 约100-1000文件/秒 (取决于文件大小和硬件)
- **内存使用**: < 100MB (无论文件数量多少)
- **支持规模**: 百万级文件数量

## 注意事项

1. **数据备份**: 运行前请备份WFS数据库
2. **独占访问**: 运行期间请确保没有其他程序访问WFS数据库
3. **磁盘空间**: 确保有足够的磁盘空间用于数据库和日志文件
4. **权限**: 确保对源目录和数据目录有读写权限
5. **文件名冲突**: 相同stem的文件会覆盖之前的记录

## 错误处理

工具具有完善的错误处理机制：
- 单个文件处理失败不会影响其他文件
- 所有错误都会记录到日志文件中
- 支持跳过无法访问的文件和目录

## 日志文件

工具会在数据目录的logs子目录下生成日志文件：
- `insert_tool.log`: 详细的操作日志

## 故障排除

### 常见问题

1. **编译失败**: 检查Go版本和CGO环境配置
2. **源目录不存在**: 检查源目录路径是否正确
3. **数据库连接失败**: 检查数据目录路径和权限
4. **内存不足**: 减少并发线程数或通道缓冲区大小

### 调试模式

修改配置文件中的日志级别为DEBUG可获得更详细的调试信息：
```json
{
  "logLevel": "DEBUG"
}
```

## 版本信息

- 版本: 1.0.0
- 基于: WFS 1.0.7
- 编译日期: 2025-07-29
