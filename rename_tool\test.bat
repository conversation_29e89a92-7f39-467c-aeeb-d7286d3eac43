@echo off
echo WFS文件名批量更名工具 - 测试脚本
echo ================================

REM 检查可执行文件是否存在
if not exist "wfs_rename_tool.exe" (
    echo 错误：找不到 wfs_rename_tool.exe
    echo 请先运行 build.bat 编译程序
    pause
    exit /b 1
)

REM 检查数据目录是否存在
set DATA_DIR=..\wfsdata
if not exist "%DATA_DIR%" (
    echo 错误：找不到数据目录 %DATA_DIR%
    echo 请确保WFS数据库目录存在
    pause
    exit /b 1
)

REM 检查数据库文件是否存在
if not exist "%DATA_DIR%\wfsdb" (
    echo 错误：找不到数据库目录 %DATA_DIR%\wfsdb
    echo 请确保WFS数据库已初始化
    pause
    exit /b 1
)

echo 数据目录: %DATA_DIR%
echo 开始测试...
echo.

REM 运行工具
wfs_rename_tool.exe %DATA_DIR%

echo.
echo 测试完成！
echo 请检查日志文件: %DATA_DIR%\logs\rename_tool.log

pause
