# WFS高性能文件入库工具 - 命令行使用说明

## 基本语法

```bash
wfs_insert_tool.exe [选项] [源目录]
```

## 命令行选项

| 选项 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `-config` | `-c` | 指定JSON配置文件路径 | `-config config.json` |
| `-source` | `-s` | 指定源目录路径 | `-source C:\files` |
| `-help` | `-h` | 显示帮助信息 | `-h` |

## 使用方式

### 1. 使用默认配置

```bash
# 使用默认配置和默认源目录 (./source)
wfs_insert_tool.exe

# 指定源目录
wfs_insert_tool.exe C:\path\to\files
wfs_insert_tool.exe ./myfiles
```

### 2. 使用配置文件

```bash
# 使用配置文件（源目录从配置文件读取）
wfs_insert_tool.exe -config wfs_insert_tool_config.json

# 使用配置文件的简写形式
wfs_insert_tool.exe -c config_small.json
```

### 3. 配置文件 + 源目录

```bash
# 配置文件 + 命令行指定源目录（命令行优先级更高）
wfs_insert_tool.exe -config config_large.json -source C:\files
wfs_insert_tool.exe -c config.json -s C:\files

# 位置参数形式
wfs_insert_tool.exe -config config.json C:\files
```

## 配置文件格式

### 基本配置文件

```json
{
  "sourceDir": "./source",
  "dataDir": "../wfsdata",
  "workerCount": 16,
  "channelBuffer": 32,
  "logLevel": "INFO",
  "progressStep": 100,
  "maxFileSize": 104857600,
  "skipExtensions": [".tmp", ".log", ".lock"],
  "compressType": 0
}
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `sourceDir` | string | `"./source"` | 源目录路径 |
| `dataDir` | string | `"../wfsdata"` | WFS数据库目录路径 |
| `workerCount` | int | CPU核心数 | 并发工作线程数 |
| `channelBuffer` | int | CPU核心数×2 | 任务通道缓冲区大小 |
| `logLevel` | string | `"INFO"` | 日志级别 |
| `progressStep` | int | 100 | 进度报告步长 |
| `maxFileSize` | int | 104857600 | 最大文件大小（字节） |
| `skipExtensions` | array | `[".tmp",".log",".lock"]` | 跳过的文件扩展名 |
| `compressType` | int | 0 | 压缩类型 |

### 文件量配置建议

#### 小文件量 (< 1万个文件)
```json
{
  "workerCount": 8,
  "channelBuffer": 16,
  "progressStep": 50,
  "maxFileSize": 52428800
}
```

#### 中等文件量 (1万-10万个文件)
```json
{
  "workerCount": 12,
  "channelBuffer": 24,
  "progressStep": 100,
  "maxFileSize": 104857600
}
```

#### 大文件量 (> 10万个文件)
```json
{
  "workerCount": 16,
  "channelBuffer": 32,
  "progressStep": 500,
  "maxFileSize": 104857600
}
```

## 使用示例

### 示例1: 基本使用

```bash
# 最简单的使用方式
wfs_insert_tool.exe ./myfiles
```

**输出**:
```
WFS高性能文件入库工具
========================
源目录: ./myfiles
数据目录: ../wfsdata
并发线程数: 8
通道缓冲区: 16

准备处理目录: ./myfiles
处理完成！
总文件数: 100
已处理数: 100
成功插入: 100
错误数量: 0
```

### 示例2: 使用配置文件

```bash
# 使用预设配置
wfs_insert_tool.exe -config config_large.json
```

### 示例3: 大量文件处理

```bash
# 处理大量文件
wfs_insert_tool.exe -config config_large.json -source D:\BigDataFiles
```

**预期输出**:
```
WFS高性能文件入库工具
========================
已加载配置文件: config_large.json
源目录: D:\BigDataFiles
并发线程数: 16
通道缓冲区: 32
最大文件大小: 100.0 MB

准备处理目录: D:\BigDataFiles

进度: 45.67% (45670/100000) | 大小: 42.3% (4.2 GB/10.0 GB) | 成功: 45650 | 错误: 20 | 用时: 5m30s | 预计剩余: 6m45s

处理完成！
总文件数: 100000
已处理数: 100000
成功插入: 99980
错误数量: 20
总文件大小: 10.0 GB
已处理大小: 9.98 GB
总用时: 12m15s
处理速度: 136.05 文件/秒, 13.9 MB/秒
```

## 优先级规则

配置参数的优先级从高到低：

1. **命令行参数** (`-source`)
2. **配置文件参数** (`-config`)
3. **位置参数** (第一个非选项参数)
4. **默认配置**

### 示例

```bash
# 配置文件中 sourceDir = "/config/path"
# 命令行指定 -source "/cmd/path"
# 位置参数 "/pos/path"
wfs_insert_tool.exe -config config.json -source /cmd/path /pos/path

# 最终使用: /cmd/path (命令行优先级最高)
```

## 错误处理

### 配置文件错误

```bash
wfs_insert_tool.exe -config invalid.json
```

**输出**:
```
加载配置文件失败: 读取配置文件失败: open invalid.json: no such file or directory
使用默认配置继续运行...
```

### 源目录错误

```bash
wfs_insert_tool.exe /invalid/path
```

**输出**:
```
处理失败: 源目录不存在: /invalid/path
```

## 性能监控

### 实时进度显示

程序运行时会显示实时进度：

```
进度: 45.67% (4567/10000) | 大小: 42.3% (4.2 GB/10.0 GB) | 成功: 4550 | 错误: 17 | 用时: 2m30s | 预计剩余: 3m15s
```

- **进度**: 当前处理百分比和文件数
- **大小**: 数据大小处理进度
- **成功**: 成功插入的文件数
- **错误**: 处理失败的文件数
- **用时**: 已用时间
- **预计剩余**: 预计剩余时间

### 最终统计

```
处理完成！
总文件数: 10000
已处理数: 10000
成功插入: 9985
错误数量: 15
总文件大小: 1.2 GB
已处理大小: 1.19 GB
总用时: 5m45s
处理速度: 28.99 文件/秒, 3.6 MB/秒
```

## 调试和故障排除

### 启用调试模式

```json
{
  "logLevel": "DEBUG",
  "progressStep": 10
}
```

### 查看日志文件

```bash
# 查看日志文件
type "数据目录\logs\insert_tool.log"
```

### 常见问题

#### 1. 文件权限错误
```
错误: 读取文件失败: permission denied
```
**解决方法**: 检查文件权限，以管理员身份运行

#### 2. 文件过大
```
错误: 文件大小超过限制: 209715200 > 104857600
```
**解决方法**: 增加maxFileSize配置或跳过大文件

#### 3. 内存不足
**解决方法**: 减少workerCount和channelBuffer

## 性能调优

### CPU密集型优化

```json
{
  "workerCount": 32,
  "channelBuffer": 64,
  "progressStep": 1000
}
```

### 内存限制优化

```json
{
  "workerCount": 4,
  "channelBuffer": 8,
  "progressStep": 50
}
```

### I/O密集型优化

```json
{
  "workerCount": 8,
  "channelBuffer": 16,
  "progressStep": 100
}
```

---

**提示**: 建议先在测试环境使用小批量文件验证配置效果，然后再处理大规模数据。
