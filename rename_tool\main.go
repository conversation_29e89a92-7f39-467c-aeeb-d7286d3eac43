// WFS文件名批量更名工具
// 基于原WFS系统改造，去除web和thrift服务，专门用于批量文件名更名
package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/donnie4w/simplelog/logging"
	_ "github.com/donnie4w/wfs/keystore"
	_ "github.com/donnie4w/wfs/stor"
	"github.com/donnie4w/wfs/sys"
)

// 进度统计结构
type ProgressStats struct {
	TotalCount     int64 // 总记录数
	ProcessedCount int64 // 已处理数
	SuccessCount   int64 // 成功更名数
	DeletedCount   int64 // 删除重复数
	ErrorCount     int64 // 错误数
	StartTime      time.Time
}

// 更名任务结构
type RenameTask struct {
	ID       int64
	OldPath  string
	NewPath  string
	FileStem string
}

// 更名工具主结构
type RenameToolConfig struct {
	DataDir       string // 数据目录
	WorkerCount   int    // 并发工作线程数
	BatchSize     int    // 批处理大小
	LogLevel      string // 日志级别
	ChannelBuffer int    // 任务通道缓冲区大小
	ProgressStep  int64  // 进度报告步长
}

// 全局变量
var (
	config *RenameToolConfig
	stats  *ProgressStats
	wg     sync.WaitGroup
	ctx    context.Context
	cancel context.CancelFunc
)

// 从JSON文件加载配置
func loadConfigFromFile(filename string) (*RenameToolConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config RenameToolConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// 解析命令行参数
func parseCommandLine() (string, string) {
	var configFile string
	var dataDir string

	flag.StringVar(&configFile, "config", "", "配置文件路径 (JSON格式)")
	flag.StringVar(&configFile, "c", "", "配置文件路径 (JSON格式) - 简写")
	flag.StringVar(&dataDir, "data", "", "数据目录路径")
	flag.StringVar(&dataDir, "d", "", "数据目录路径 - 简写")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS文件名批量更名工具\n\n")
		fmt.Fprintf(os.Stderr, "用法:\n")
		fmt.Fprintf(os.Stderr, "  %s [选项] [数据目录]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\n示例:\n")
		fmt.Fprintf(os.Stderr, "  %s ../wfsdata                                    # 使用默认配置\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -config config.json                          # 使用配置文件\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -config config.json -data /path/to/wfsdata   # 配置文件+数据目录\n", os.Args[0])
	}

	flag.Parse()

	// 如果没有通过-data指定，检查位置参数
	if dataDir == "" && flag.NArg() > 0 {
		dataDir = flag.Arg(0)
	}

	return configFile, dataDir
}

func init() {
	// 初始化默认配置
	config = &RenameToolConfig{
		DataDir:       "../wfsdata",
		WorkerCount:   runtime.NumCPU(),
		BatchSize:     100,
		LogLevel:      "INFO",
		ChannelBuffer: runtime.NumCPU() * 2, // 通道缓冲区大小
		ProgressStep:  1000,                 // 每1000条记录报告一次进度
	}

	// 初始化统计
	stats = &ProgressStats{
		StartTime: time.Now(),
	}
}

// 提取文件stem（去除路径和扩展名）
func extractFileStem(fullPath string) string {
	// 获取文件名（去除路径）
	filename := filepath.Base(fullPath)

	// 去除扩展名
	ext := filepath.Ext(filename)
	if ext != "" {
		filename = strings.TrimSuffix(filename, ext)
	}

	return filename
}

// 检查文件stem是否已存在（排除当前文件本身）
func checkFileStemExists(stem string, currentPath string) bool {
	// 使用sys.SearchLike查找以stem开头的文件
	results := sys.SearchLike(stem)
	for _, result := range results {
		// 排除当前文件本身
		if result.Path != currentPath && extractFileStem(result.Path) == stem {
			return true
		}
	}
	return false
}

// 处理单个文件记录
func processFileRecord(task *RenameTask) error {
	// 提取文件stem
	stem := extractFileStem(task.OldPath)
	task.FileStem = stem
	task.NewPath = stem

	// 如果文件名已经是目标格式，跳过处理
	if task.OldPath == stem {
		atomic.AddInt64(&stats.ProcessedCount, 1)
		logging.Debug(fmt.Sprintf("跳过处理（已是目标格式）: %s", task.OldPath))
		return nil
	}

	// 检查目标stem是否已存在（排除当前文件）
	if checkFileStemExists(stem, task.OldPath) {
		// 如果存在其他同名文件，删除当前记录
		if err := sys.DelData(task.OldPath); err != nil {
			atomic.AddInt64(&stats.ErrorCount, 1)
			return fmt.Errorf("删除重复记录失败: %v", err)
		}
		atomic.AddInt64(&stats.DeletedCount, 1)
		logging.Info(fmt.Sprintf("删除重复记录: %s -> %s (目标已存在)", task.OldPath, stem))
	} else {
		// 执行更名操作
		if err := sys.Modify(task.OldPath, stem); err != nil {
			atomic.AddInt64(&stats.ErrorCount, 1)
			return fmt.Errorf("更名失败: %v", err)
		}
		atomic.AddInt64(&stats.SuccessCount, 1)
		logging.Info(fmt.Sprintf("更名成功: %s -> %s", task.OldPath, stem))
	}

	atomic.AddInt64(&stats.ProcessedCount, 1)
	return nil
}

// 工作线程
func worker(taskChan <-chan *RenameTask) {
	defer wg.Done()

	for {
		select {
		case task, ok := <-taskChan:
			if !ok {
				return
			}

			if err := processFileRecord(task); err != nil {
				logging.Error(fmt.Sprintf("处理任务失败 [ID:%d]: %v", task.ID, err))
			}

		case <-ctx.Done():
			return
		}
	}
}

// 显示进度
func showProgress() {
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			elapsed := time.Since(stats.StartTime)
			processed := atomic.LoadInt64(&stats.ProcessedCount)
			total := atomic.LoadInt64(&stats.TotalCount)
			success := atomic.LoadInt64(&stats.SuccessCount)
			deleted := atomic.LoadInt64(&stats.DeletedCount)
			errors := atomic.LoadInt64(&stats.ErrorCount)

			var progress float64
			if total > 0 {
				progress = float64(processed) / float64(total) * 100
			}

			var eta time.Duration
			if processed > 0 && total > processed {
				rate := float64(processed) / elapsed.Seconds()
				remaining := total - processed
				eta = time.Duration(float64(remaining)/rate) * time.Second
			}

			fmt.Printf("\r进度: %.2f%% (%d/%d) | 成功: %d | 删除: %d | 错误: %d | 用时: %v | 预计剩余: %v",
				progress, processed, total, success, deleted, errors,
				elapsed.Truncate(time.Second), eta.Truncate(time.Second))

		case <-ctx.Done():
			return
		}
	}
}

// 流式处理记录 - 不一次性加载所有记录到内存
func processRecordsStream(taskChan chan<- *RenameTask) error {
	defer close(taskChan)

	// 获取总记录数和最大序列号
	totalCount := sys.Count()
	maxSeq := sys.Seq()
	atomic.StoreInt64(&stats.TotalCount, totalCount)

	logging.Info(fmt.Sprintf("开始流式处理数据库，总记录数: %d, 最大序列号: %d", totalCount, maxSeq))

	// 分批获取记录 - 从最大序列号开始向下查找
	batchSize := int64(config.BatchSize)
	currentSeq := maxSeq
	processedCount := int64(0)

	for currentSeq > 0 {
		limit := batchSize
		if currentSeq < batchSize {
			limit = currentSeq
		}

		logging.Debug(fmt.Sprintf("查询序列号范围: %d 向下 %d 条", currentSeq, limit))
		records := sys.SearchLimit(currentSeq, limit)

		logging.Debug(fmt.Sprintf("本批次获取到 %d 条记录", len(records)))

		// 立即处理这批记录，不存储到内存
		for _, record := range records {
			task := &RenameTask{
				ID:      record.Id,
				OldPath: record.Path,
			}

			select {
			case taskChan <- task:
				processedCount++
			case <-ctx.Done():
				logging.Info(fmt.Sprintf("处理被中断，已发送 %d 条记录", processedCount))
				return ctx.Err()
			}
		}

		// 更新当前序列号位置
		currentSeq -= limit

		// 定期输出进度
		if processedCount%config.ProgressStep == 0 && processedCount > 0 {
			logging.Info(fmt.Sprintf("已发送 %d 条记录到处理队列", processedCount))
		}
	}

	logging.Info(fmt.Sprintf("流式扫描完成，共发送 %d 条记录", processedCount))
	return nil
}

// 主处理函数 - 使用流式处理
func processAllFiles() error {
	// 创建上下文
	ctx, cancel = context.WithCancel(context.Background())
	defer cancel()

	// 检查是否有记录需要处理
	totalCount := sys.Count()
	if totalCount == 0 {
		fmt.Println("没有找到需要处理的记录")
		return nil
	}

	fmt.Printf("准备处理 %d 条记录...\n", totalCount)

	// 创建任务通道 - 使用配置的缓冲区大小以控制内存使用
	taskChan := make(chan *RenameTask, config.ChannelBuffer)

	// 启动工作线程
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go worker(taskChan)
	}

	// 启动进度显示
	go showProgress()

	// 启动流式记录处理
	go func() {
		if err := processRecordsStream(taskChan); err != nil {
			logging.Error(fmt.Sprintf("流式处理失败: %v", err))
			cancel()
		}
	}()

	// 等待所有任务完成
	wg.Wait()

	// 显示最终统计
	fmt.Printf("\n\n处理完成！\n")
	fmt.Printf("总记录数: %d\n", atomic.LoadInt64(&stats.TotalCount))
	fmt.Printf("已处理数: %d\n", atomic.LoadInt64(&stats.ProcessedCount))
	fmt.Printf("成功更名: %d\n", atomic.LoadInt64(&stats.SuccessCount))
	fmt.Printf("删除重复: %d\n", atomic.LoadInt64(&stats.DeletedCount))
	fmt.Printf("错误数量: %d\n", atomic.LoadInt64(&stats.ErrorCount))
	fmt.Printf("总用时: %v\n", time.Since(stats.StartTime).Truncate(time.Second))

	return nil
}

// 初始化系统
func initSystem() error {
	// 设置数据目录
	sys.WFSDATA = config.DataDir

	// 初始化日志
	logDir := filepath.Join(config.DataDir, "logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	logging.SetRollingFile(logDir, "rename_tool.log", 100, logging.MB)

	// 设置日志级别
	switch strings.ToUpper(config.LogLevel) {
	case "DEBUG":
		logging.SetLevel(logging.LEVEL_DEBUG)
	case "INFO":
		logging.SetLevel(logging.LEVEL_INFO)
	case "WARN":
		logging.SetLevel(logging.LEVEL_WARN)
	case "ERROR":
		logging.SetLevel(logging.LEVEL_ERROR)
	default:
		logging.SetLevel(logging.LEVEL_INFO)
	}

	// 初始化密钥存储
	sys.KeyStoreInit(config.DataDir)

	// 初始化存储引擎 - 通过sys.Serve调用
	if server, ok := sys.Serve.Get(1); ok {
		if err := server.Serve(); err != nil {
			return fmt.Errorf("初始化存储引擎失败: %v", err)
		}
	} else {
		return fmt.Errorf("存储引擎服务未注册")
	}

	return nil
}

func main() {
	fmt.Println("WFS文件名批量更名工具")
	fmt.Println("========================")

	// 解析命令行参数
	configFile, dataDir := parseCommandLine()

	// 如果指定了配置文件，加载配置
	if configFile != "" {
		if newConfig, err := loadConfigFromFile(configFile); err != nil {
			fmt.Printf("加载配置文件失败: %v\n", err)
			fmt.Println("使用默认配置继续运行...")
		} else {
			config = newConfig
			fmt.Printf("已加载配置文件: %s\n", configFile)
		}
	}

	// 命令行指定的数据目录优先级最高
	if dataDir != "" {
		config.DataDir = dataDir
	}

	// 显示当前配置
	fmt.Printf("数据目录: %s\n", config.DataDir)
	fmt.Printf("并发线程数: %d\n", config.WorkerCount)
	fmt.Printf("批处理大小: %d\n", config.BatchSize)
	fmt.Printf("通道缓冲区: %d\n", config.ChannelBuffer)
	fmt.Printf("进度报告步长: %d\n", config.ProgressStep)
	fmt.Printf("日志级别: %s\n", config.LogLevel)
	fmt.Println()

	// 初始化系统
	if err := initSystem(); err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		os.Exit(1)
	}

	// 开始处理
	if err := processAllFiles(); err != nil {
		fmt.Printf("处理失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("程序执行完成！")
}
