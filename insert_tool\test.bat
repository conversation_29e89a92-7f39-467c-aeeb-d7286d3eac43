@echo off
echo WFS高性能文件入库工具 - 测试脚本
echo ================================

REM 检查可执行文件是否存在
if not exist "wfs_insert_tool.exe" (
    echo 错误：找不到 wfs_insert_tool.exe
    echo 请先运行 build.bat 编译程序
    pause
    exit /b 1
)

REM 创建测试目录和文件
echo 创建测试环境...
if not exist "test_source" mkdir test_source
if not exist "test_source\subdir1" mkdir test_source\subdir1
if not exist "test_source\subdir2" mkdir test_source\subdir2

REM 创建测试文件
echo 这是测试文件1的内容 > test_source\file1.txt
echo 这是测试文件2的内容 > test_source\file2.txt
echo 这是子目录1中的文件 > test_source\subdir1\subfile1.txt
echo 这是子目录2中的文件 > test_source\subdir2\subfile2.txt
echo 这是一个JSON文件 > test_source\data.json
echo 临时文件内容 > test_source\temp.tmp

echo 测试文件创建完成！
echo.

REM 显示测试文件
echo 测试文件列表：
dir /s test_source
echo.

REM 测试1：使用默认配置
echo 测试1：使用默认配置处理测试文件
echo =====================================
wfs_insert_tool.exe test_source
echo.

REM 测试2：使用小文件量配置
echo 测试2：使用小文件量配置
echo ========================
wfs_insert_tool.exe -config config_small.json -source test_source
echo.

REM 测试3：显示帮助信息
echo 测试3：显示帮助信息
echo ==================
wfs_insert_tool.exe -h
echo.

echo 所有测试完成！
echo.
echo 清理测试文件...
rmdir /s /q test_source 2>nul

echo 测试脚本执行完成！
pause
